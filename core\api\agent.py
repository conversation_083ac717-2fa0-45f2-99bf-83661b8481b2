from fastapi import APIRouter, Depends, HTTPException, Query

from core.vo.user_vo import UserVO
from core.config.security import check_user
from core.services.database import db_manager
from core.services.database.crud.ai_agent_dao import ai_agent_crud

router = APIRouter(prefix="/agent")

@router.get("")
async def query_agents():
    """
        查询所有agent列表

        Returns:
            agent列表
    """
     
    try:
        agents = await ai_agent_crud.query_agents()
        print(agents)
        return {
            "code": 200,
            "data": agents,
            "message": "查询成功",
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"数据库查询错误: {str(e)}") from e
    