from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select
from datetime import datetime, timezone

from core.services.database.crud.base import CRUDBase
from core.services.database.schemas.prompt_template import (
    PromptTemplateTable,
    PromptTemplateCreate,
    PromptTemplateUpdate,
)


class CRUDPromptTemplate(CRUDBase[PromptTemplateTable, PromptTemplateCreate, PromptTemplateUpdate]):
    """提示词模板CRUD操作实现"""

    async def get_by_type(
        self, 
        db: AsyncSession, 
        *, 
        template_type: str, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[PromptTemplateTable]:
        """根据模板类型获取提示词模板列表，按order字段升序排序"""
        query = (
            select(PromptTemplateTable)
            .where(PromptTemplateTable.type == template_type)
            .order_by(PromptTemplateTable.order.asc(), PromptTemplateTable.id.asc())
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def get_by_group(
        self, 
        db: AsyncSession, 
        *, 
        group: str, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[PromptTemplateTable]:
        """根据分组获取提示词模板列表，按order字段升序排序"""
        query = (
            select(PromptTemplateTable)
            .where(PromptTemplateTable.group == group)
            .order_by(PromptTemplateTable.order.asc(), PromptTemplateTable.id.asc())
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def get_by_type_and_group(
        self, 
        db: AsyncSession, 
        *, 
        template_type: str, 
        group: str, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[PromptTemplateTable]:
        """根据模板类型和分组获取提示词模板列表，按order字段升序排序"""
        query = (
            select(PromptTemplateTable)
            .where(
                PromptTemplateTable.type == template_type,
                PromptTemplateTable.group == group
            )
            .order_by(PromptTemplateTable.order.asc(), PromptTemplateTable.id.asc())
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def get_all_ordered(
        self, 
        db: AsyncSession, 
        *, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[PromptTemplateTable]:
        """获取所有提示词模板，按order字段升序排序"""
        query = (
            select(PromptTemplateTable)
            .order_by(PromptTemplateTable.order.asc(), PromptTemplateTable.id.asc())
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def get_groups(self, db: AsyncSession) -> List[str]:
        """获取所有不同的分组列表"""
        query = select(PromptTemplateTable.group).distinct()
        result = await db.execute(query)
        return result.scalars().all()

    async def update(
        self, 
        db: AsyncSession, 
        *, 
        db_obj: PromptTemplateTable, 
        obj_in: PromptTemplateUpdate
    ) -> PromptTemplateTable:
        """更新提示词模板，自动设置updated_at时间"""
        obj_data = obj_in.model_dump(exclude_unset=True)
        obj_data["updated_at"] = datetime.now(timezone.utc)
        
        for field, value in obj_data.items():
            setattr(db_obj, field, value)
        
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def search_by_title_or_description(
        self, 
        db: AsyncSession, 
        *, 
        search_term: str, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[PromptTemplateTable]:
        """根据标题或描述搜索提示词模板"""
        search_pattern = f"%{search_term}%"
        query = (
            select(PromptTemplateTable)
            .where(
                (PromptTemplateTable.title.ilike(search_pattern)) |
                (PromptTemplateTable.description.ilike(search_pattern))
            )
            .order_by(PromptTemplateTable.order.asc(), PromptTemplateTable.id.asc())
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()


# 创建CRUD实例
prompt_template_crud = CRUDPromptTemplate(PromptTemplateTable)
