from typing import Optional, List
from core.api.response import StandardResponse
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body

from core.vo.user_vo import UserVO
from core.config.security import check_user
from core.services.database import db_manager
from core.services.database.crud.prompt_template import prompt_template_crud
from core.services.database.schemas.prompt_template import (
    PromptTemplateCreate,
    PromptTemplateUpdate,
    PromptTemplateRead,
)
from pydantic import BaseModel
from sqlalchemy import text

router = APIRouter(prefix="/prompt-template", tags=["PromptTemplate"])


@router.get("", response_model=StandardResponse[PromptTemplateRead])
async def query_prompt_templates(
    template_type: Optional[str] = Query(
        None, description="模板类型过滤：user 或 system"
    ),
    group: Optional[str] = Query(None, description="分组过滤"),
    search: Optional[str] = Query(None, description="搜索关键词（标题或描述）"),
    skip: int = Query(0, description="跳过的记录数"),
    limit: int = Query(100, description="返回的记录数"),
    user: UserVO = Depends(check_user),
):
    """
    查询提示词模板列表

    Args:
        template_type: 模板类型过滤
        group: 分组过滤
        search: 搜索关键词
        skip: 跳过的记录数
        limit: 返回的记录数
        user: 当前用户信息

    Returns:
        提示词模板列表
    """
    try:
        async with db_manager.session() as session:
            if search:
                # 搜索模式
                templates = await prompt_template_crud.search_by_title_or_description(
                    session, search_term=search, skip=skip, limit=limit
                )
            elif template_type and group:
                # 按类型和分组过滤
                templates = await prompt_template_crud.get_by_type_and_group(
                    session,
                    template_type=template_type,
                    group=group,
                    skip=skip,
                    limit=limit,
                )
            elif template_type:
                # 按类型过滤
                templates = await prompt_template_crud.get_by_type(
                    session, template_type=template_type, skip=skip, limit=limit
                )
            elif group:
                # 按分组过滤
                templates = await prompt_template_crud.get_by_group(
                    session, group=group, skip=skip, limit=limit
                )
            else:
                # 获取所有模板
                templates = await prompt_template_crud.get_all_ordered(
                    session, skip=skip, limit=limit
                )

            return StandardResponse(data=templates, message="查询成功")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"数据库查询错误: {str(e)}") from e


@router.get("/client", response_model=StandardResponse[PromptTemplateRead])
async def get_all_client_templates(
    user: UserVO = Depends(check_user),
):
    try:
        async with db_manager.session() as session:
            query = text(
                "SELECT * FROM prompt_template WHERE (created_by = :created_by and type = 'user') OR type = 'system'"
            )
        templates = session.execute(query, {"created_by": user.userId})
        return StandardResponse(data=templates)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"模板查询失败: {str(e)}") from e


@router.get("/groups")
async def get_template_groups(
    user: UserVO = Depends(check_user),
):
    """
    获取所有模板分组列表

    Args:
        user: 当前用户信息

    Returns:
        分组列表
    """
    try:
        async with db_manager.session() as session:
            groups = await prompt_template_crud.get_groups(session)
            return {
                "code": 200,
                "data": groups,
                "message": "查询成功",
            }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"数据库查询错误: {str(e)}") from e


@router.get("/{template_id}", response_model=PromptTemplateRead)
async def get_prompt_template(
    template_id: int = Path(..., description="模板ID"),
    user: UserVO = Depends(check_user),
):
    """
    根据ID获取单个提示词模板

    Args:
        template_id: 模板ID
        user: 当前用户信息

    Returns:
        提示词模板详情
    """
    try:
        async with db_manager.session() as session:
            template = await prompt_template_crud.get(session, _id=template_id)
            if not template or (
                template.created_by != user.userId and template.type == "user"
            ):
                raise HTTPException(status_code=404, detail="模板不存在")
            return template
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"数据库查询错误: {str(e)}") from e


@router.post("", response_model=PromptTemplateRead)
async def create_prompt_template(
    template_data: PromptTemplateCreate = Body(..., description="模板创建数据"),
    user: UserVO = Depends(check_user),
):
    """
    创建新的提示词模板

    Args:
        template_data: 模板创建数据
        user: 当前用户信息

    Returns:
        创建的模板信息
    """
    try:
        async with db_manager.session() as session:
            template = await prompt_template_crud.create(
                session, obj_input=template_data
            )
            return template
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建模板失败: {str(e)}") from e


@router.put("/{template_id}", response_model=PromptTemplateRead)
async def update_prompt_template(
    template_id: int = Path(..., description="模板ID"),
    template_data: PromptTemplateUpdate = Body(..., description="模板更新数据"),
    user: UserVO = Depends(check_user),
):
    """
    更新提示词模板

    Args:
        template_id: 模板ID
        template_data: 模板更新数据
        user: 当前用户信息

    Returns:
        更新后的模板信息
    """
    try:
        async with db_manager.session() as session:
            # 检查模板是否存在
            existing_template = await prompt_template_crud.get(session, _id=template_id)
            if not existing_template:
                raise HTTPException(status_code=404, detail="模板不存在")

            # 更新模板
            updated_template = await prompt_template_crud.update(
                session, db_obj=existing_template, obj_in=template_data
            )
            return updated_template
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新模板失败: {str(e)}") from e


@router.delete("/{template_id}")
async def delete_prompt_template(
    template_id: int = Path(..., description="模板ID"),
    user: UserVO = Depends(check_user),
):
    """
    删除提示词模板

    Args:
        template_id: 模板ID
        user: 当前用户信息

    Returns:
        删除结果
    """
    try:
        async with db_manager.session() as session:
            # 检查模板是否存在
            existing_template = await prompt_template_crud.get(session, _id=template_id)
            if not existing_template:
                raise HTTPException(status_code=404, detail="模板不存在")

            # 删除模板
            await prompt_template_crud.remove(session, _id=template_id)
            return {
                "code": 200,
                "message": "删除成功",
            }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除模板失败: {str(e)}") from e


class ChangeOrderBody(BaseModel):
    prev_id: int | None


@router.put("/user/{template_id}/order")
async def change_order(
    template_id: str = Path(..., description="模板id"),
    data: ChangeOrderBody = Body(..., description="修改排序参数"),
    user: UserVO = Depends(check_user),
):
    """
    自定义模板修改排序
    """
    # 判断模板是否当前用户所有
    try:
        async with db_manager.session() as session:
            prev_template = await prompt_template_crud.get(session, _id=data.prev_id)
            template_id = int(template_id)
            existing_template = await prompt_template_crud.get(
                session, _id=template_id
            )
            user_id = str(user.userId)
            if existing_template.created_by != user_id:
                raise HTTPException(status_code=403, detail="没有修改权限")
            old_order = existing_template.order
            new_order = prev_template.order if prev_template is not None else 0

            # TODO: 锁行
            if old_order > new_order:
                order_update = text("""
                    UPDATE prompt_template
                    SET "order" = "order" + 1 
                    WHERE "order" > :new_order AND "order" <= :old_order
                    AND created_by = :user_id
                """)
            else:
                order_update = text("""
                    UPDATE prompt_template
                    SET "order" = "order" - 1 
                    WHERE "order" > :old_order AND "order" <= :new_order
                    AND created_by = :user_id
                """)

            # 临时设置 order = -1
            await session.execute(
                text("""
                UPDATE prompt_template SET "order" = -1 WHERE id = :id;
                """),
                {
                    "id": template_id,
                    "old_order": old_order,
                },
            )

            await session.execute(
                order_update,
                {"old_order": old_order, "new_order": new_order, "user_id": user_id},
            )

            # 设置新的 order
            await session.execute(
                text("""
                UPDATE prompt_template SET "order" = :new_order WHERE id = :id;
                """),
                {
                    "id": template_id,
                    "new_order": new_order,
                },
            )

            return StandardResponse(data="")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"修改排序失败: {str(e)}") from e
