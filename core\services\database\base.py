from typing import AsyncGenerator, Optional
import os
from core.config.app_logger import logger
from contextlib import asynccontextmanager

from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlmodel import SQLModel

from core.config.app_config import config

class DatabaseManager:
    """数据库连接管理器"""

    _instance = None

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(DatabaseManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self, db_url: Optional[str] = None):
        if self._initialized:
            return

        self.db_url = db_url or config.database.url
        self.engine = create_async_engine(
            self.db_url,
            echo=os.getenv("DB_ECHO", "false").lower() == "true",
            pool_pre_ping=True,
        )
        self.async_session_maker = sessionmaker(
            self.engine, class_=AsyncSession, expire_on_commit=False
        )
        self._initialized = True

    async def init_db(self):
        """初始化数据库表结构"""
        async with self.engine.begin() as conn:
            # 创建所有表
            await conn.run_sync(SQLModel.metadata.create_all)
            logger.info("Database tables initialized")

    async def get_session(self) -> AsyncSession:
        """获取数据库会话"""
        return self.async_session_maker()

    @asynccontextmanager
    async def session(self) -> AsyncGenerator[AsyncSession, None]:
        """会话上下文管理器"""
        session = await self.get_session()
        try:
            yield session
            await session.commit()
        except Exception as e:
            await session.rollback()
            logger.error(f"Database session error: {e}")
            raise
        finally:
            await session.close()
