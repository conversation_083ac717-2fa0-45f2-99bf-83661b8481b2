from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body
from pydantic import BaseModel
from sqlalchemy import text

from core.vo.user_vo import UserVO
from core.config.security import check_user
from core.services.database import db_manager
from core.services.database.crud.message import message_curd
from core.services.database.crud.conversation import conversation_curd

router = APIRouter(prefix="/message")


class MessageRatingRequest(BaseModel):
    """消息评价请求模型"""

    rating: str  # "like", "dislike", 或 "" (取消评价)


@router.get("")
async def query_messages(
    conversation_id: str = Query(..., description="会话ID"),
    user: UserVO = Depends(check_user),
):
    """
    根据会话ID查询消息列表

    Args:
        conversation_id: 会话ID
        user: 当前用户信息

    Returns:
        消息列表
    """
    try:
        async with db_manager.session() as session:
            # 如果会话不属于当前用户，抛出异常
            conversation = await conversation_curd.get_by_conversation_id(
                session, _id=conversation_id
            )
            if not conversation:
                raise HTTPException(status_code=404, detail="会话不存在")
            if conversation[0].user_id != user.userId:
                raise HTTPException(status_code=403, detail="会话不属于当前用户")

            # 查询指定会话ID的所有消息
            messages = await message_curd.get_by_conversation_id(
                session, conversation_id=conversation_id
            )

            return {
                "code": 200,
                "data": messages,
                "message": "查询成功",
            }
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail=f"数据库查询错误: {str(e)}") from e


@router.post("/{message_id}/rate")
async def rate_message(
    message_id: str = Path(..., description="消息ID"),
    request: MessageRatingRequest = Body(..., description="评价请求"),
    user: UserVO = Depends(check_user),
):
    """
    对消息进行评价

    Args:
        message_id: 消息ID
        request: 评价请求，包含评价类型
        user: 当前用户信息

    Returns:
        评价结果
    """
    try:
        # 验证评价类型
        if request.rating not in ["like", "dislike", ""]:
            raise HTTPException(
                status_code=400, detail="评价类型必须是 'like'、'dislike' 或空字符串"
            )

        async with db_manager.session() as session:
            # 检查消息是否存在
            message = await message_curd.get(session, _id=message_id)
            if not message:
                raise HTTPException(status_code=404, detail="消息不存在")

            # 检查消息所属的会话是否属于当前用户
            conversation = await conversation_curd.get_by_conversation_id(
                session, _id=message.conversation_id
            )
            if not conversation:
                raise HTTPException(status_code=404, detail="会话不存在")
            if conversation[0].user_id != user.userId:
                raise HTTPException(status_code=403, detail="无权限评价此消息")

            # 执行评价
            updated_message = await message_curd.rate_message(
                session,
                message_id=message_id,
                rating=request.rating if request.rating else None,
                user_id=user.userId,
            )

            if not updated_message:
                raise HTTPException(status_code=404, detail="消息不存在")

            return {
                "code": 200,
                "data": {
                    "message_id": message_id,
                    "like_count": updated_message.like_count,
                    "dislike_count": updated_message.dislike_count,
                    "user_rating": updated_message.user_rating,
                },
                "message": "评价成功",
            }
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail=f"评价失败: {str(e)}") from e


@router.get("/{message_id}/rating")
async def get_message_rating(
    message_id: str = Path(..., description="消息ID"),
    user: UserVO = Depends(check_user),
):
    """
    获取消息评价信息

    Args:
        message_id: 消息ID
        user: 当前用户信息

    Returns:
        消息评价信息
    """
    try:
        async with db_manager.session() as session:
            # 检查消息是否存在
            message = await message_curd.get(session, _id=message_id)
            if not message:
                raise HTTPException(status_code=404, detail="消息不存在")

            # 检查消息所属的会话是否属于当前用户
            conversation = await conversation_curd.get_by_conversation_id(
                session, _id=message.conversation_id
            )
            if not conversation:
                raise HTTPException(status_code=404, detail="会话不存在")
            if conversation[0].user_id != user.userId:
                raise HTTPException(status_code=403, detail="无权限查看此消息评价")

            # 获取评价信息
            rating_info = await message_curd.get_message_rating(
                session, message_id=message_id
            )

            if not rating_info:
                raise HTTPException(status_code=404, detail="消息不存在")

            return {
                "code": 200,
                "data": rating_info,
                "message": "查询成功",
            }
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}") from e

