from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select

from core.services.database.crud.base import CRUDBase
from core.services.database.schemas.conversation import ConversationTable
from core.services.database.schemas.conversation import (
    ConversationCreate,
    ConversationUpdate,
)


class CRUDConversation(
    CRUDBase[ConversationTable, ConversationCreate, ConversationUpdate]
):
    """对话记录CRUD操作实现"""

    async def get_by_user_id(
        self, db: AsyncSession, *, user_id: int, skip: int = 0, limit: int = 100
    ) -> List[ConversationTable]:
        """根据用户ID获取对话列表（排除已删除的对话）"""
        query = (
            select(ConversationTable)
            .where(ConversationTable.user_id == user_id)
            .where(ConversationTable.is_deleted.is_(False))
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def get_by_agent_code(
        self, db: AsyncSession, *, agent_code: int, skip: int = 0, limit: int = 100
    ) -> List[ConversationTable]:
        """根据代理ID获取对话列表（排除已删除的对话）"""
        query = (
            select(ConversationTable)
            .where(ConversationTable.agent_code == agent_code)
            .where(ConversationTable.is_deleted.is_(False))
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def get_by_conversation_id(
        self, db: AsyncSession, *, _id: str, include_deleted: bool = False
    ) -> List[ConversationTable]:
        """根据会话ID获取完整对话"""
        query = (
            select(ConversationTable)
            .where(ConversationTable.id == _id)
            .order_by(ConversationTable.created_at)
        )
        if not include_deleted:
            query = query.where(ConversationTable.is_deleted.is_(False))
        result = await db.execute(query)
        return result.scalars().all()

    async def soft_delete(self, db: AsyncSession, *, _id: str) -> Optional[ConversationTable]:
        """软删除对话记录"""
        obj = await self.get(db, _id)
        if obj:
            update_data = ConversationUpdate(is_deleted=True)
            return await self.update(db=db, db_obj=obj, obj_input=update_data)
        return None

    async def restore(self, db: AsyncSession, *, _id: str) -> Optional[ConversationTable]:
        """恢复已删除的对话记录"""
        # 需要包含已删除的记录才能找到被软删除的对话
        obj = await self.get(db, _id, include_deleted=True)
        if obj:
            update_data = ConversationUpdate(is_deleted=False)
            return await self.update(db=db, db_obj=obj, obj_input=update_data)
        return None

    async def get_deleted_conversations(
        self, db: AsyncSession, *, user_id: int, skip: int = 0, limit: int = 100
    ) -> List[ConversationTable]:
        """获取已删除的对话列表（管理员功能）"""
        query = (
            select(ConversationTable)
            .where(ConversationTable.user_id == user_id)
            .where(ConversationTable.is_deleted.is_(True))
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()


conversation_curd = CRUDConversation(ConversationTable)
