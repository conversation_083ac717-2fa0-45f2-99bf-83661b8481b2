from typing import Optional
from sqlmodel import Field, SQLModel


class AIAgentBase(SQLModel):
    """AI Agent 基础模型"""
    agent_code: str = Field(unique=True, nullable=False,description="Agent 编码")
    agent_name: str = Field(nullable=False,description="Agent 名称")
    target_type: str = Field(default=None,description="连接目标对象")
    base_url: str = Field(default=None,description="访问大模型的api地址")
    api_key: str = Field(default=None,description="访问大模型的api key")
    router_url: str = Field(default=None, description="路由URL")  
    prompt_text: Optional[str] = Field(default=None,description="提示词文本")
    agent_desc: Optional[str] = Field(default=None,description="智能体描述信息")
    status: int = Field(default=1, description="状态")  


class AIAgentTable(AIAgentBase, table=True):
    """数据库中的 AI Agent 模型"""
    __tablename__ = "ai_agent"
    id: int = Field(default=None, primary_key=True)


class AIAgentCreate(AIAgentBase):
    """创建 AI Agent 模型"""


class AIAgentUpdate(SQLModel):
    """更新 AI Agent 模型"""
    agent_code: Optional[str] = None
    agent_name: Optional[str] = None
    target_type: Optional[str] = None
    base_url: Optional[str] = None
    api_key: Optional[str] = None
    router_url: Optional[str] = None
    prompt_text: Optional[str] = None
    agent_desc: Optional[str] = None
    status: Optional[int] = None


class AIAgentInDBBase(AIAgentBase):
    """数据库中的 AI Agent 模型"""
    id: int