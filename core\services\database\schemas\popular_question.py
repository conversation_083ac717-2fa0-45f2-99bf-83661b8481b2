from typing import Optional
from sqlmodel import Field, SQLModel


class PopularQuestionBase(SQLModel):
    """热门问题基础模型"""
    agent_code: str = Field(index=True, description="智能体编码")
    prompt: str = Field(description="问题提示词")
    heat: int = Field(default=0, description="热度值")


class PopularQuestionTable(PopularQuestionBase, table=True):
    """数据库中的热门问题模型"""
    __tablename__ = "popular_question"
    id: int = Field(default=None, primary_key=True)


class PopularQuestionCreate(PopularQuestionBase):
    """创建热门问题的模型"""


class PopularQuestionUpdate(SQLModel):
    """更新热门问题的模型"""
    agent_code: Optional[str] = None
    prompt: Optional[str] = None
    heat: Optional[int] = None


class PopularQuestionRead(PopularQuestionBase):
    """API响应中的热门问题模型"""
    id: int
